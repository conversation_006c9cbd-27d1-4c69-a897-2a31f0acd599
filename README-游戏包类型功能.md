# 游戏包类型功能使用说明

## 功能概述

游戏包管理系统现在支持两种包类型：
- **本地包**：通过文件上传的方式管理游戏包
- **第三方包**：通过URL地址引用外部游戏包

## 使用方法

### 创建本地包

1. 进入游戏包管理页面
2. 点击"新增"按钮
3. 在"类型"字段选择"本地包"
4. 在"包地址"区域：
   - 点击"上传文件"按钮直接上传游戏包文件
   - 或点击"选择文件"按钮从已上传的附件中选择
5. 填写其他必要信息（版本、描述等）
6. 点击"提交"保存

### 创建第三方包

1. 进入游戏包管理页面
2. 点击"新增"按钮
3. 在"类型"字段选择"第三方包"
4. 在"包地址"区域：
   - 输入第三方包的完整URL地址
   - 地址必须以 `https://` 或 `http://` 开头
   - 例如：`https://cdn.example.com/games/mygame-v1.0.zip`
5. 填写其他必要信息（版本、描述等）
6. 点击"提交"保存

### 编辑游戏包

1. 在游戏包列表中点击"编辑"按钮
2. 系统会根据当前包类型自动显示对应的输入方式
3. 可以修改包类型，界面会相应切换
4. 修改完成后点击"提交"保存

## 验证规则

### 第三方包URL要求

- **不能为空**：必须提供有效的URL地址
- **格式正确**：必须是有效的URL格式
- **协议限制**：只支持 `https://` 和 `http://` 协议
- **示例有效URL**：
  - `https://example.com/game.zip`
  - `http://cdn.example.com/games/v1.0/game.zip`
  - `https://*************/game.zip`

### 无效URL示例

- `ftp://example.com/game.zip` （不支持FTP协议）
- `example.com/game.zip` （缺少协议）
- `file:///local/game.zip` （不支持file协议）

## 技术特点

- **实时验证**：输入时即时检查URL格式
- **双重保护**：前端和后端都进行验证
- **向后兼容**：完全保持原有本地包功能
- **用户友好**：直观的界面切换和清晰的错误提示

## 注意事项

1. **网络访问**：第三方包需要确保服务器能够访问提供的URL
2. **文件格式**：第三方包应该是有效的游戏包文件（如ZIP格式）
3. **安全性**：建议使用HTTPS协议确保传输安全
4. **可用性**：确保第三方包URL长期可用，避免链接失效

## 错误处理

如果遇到以下错误信息，请按提示修正：

- **"第三方包地址不能为空"**：请输入有效的URL地址
- **"第三方包地址格式不正确"**：请检查URL格式是否正确
- **"第三方包地址必须以 https:// 或 http:// 开头"**：请使用正确的协议前缀

## 支持

如有问题，请联系技术支持团队。
