<?php

/**
 * 游戏包预览功能测试脚本
 * 
 * 此脚本用于测试本地包和第三方包的预览功能是否正常工作
 */

require_once __DIR__ . '/vendor/autoload.php';

use app\service\GameService;
use app\service\GamePackageService;
use app\admin\controller\GamePackageController;
use app\controller\GameController;

echo "=== 游戏包预览功能测试 ===\n";
echo "注意：游戏预览地址保持不变，只有游戏show页面的iframe根据包类型加载不同地址\n\n";

// 测试用例数据
$testCases = [
    [
        'name' => '本地包测试',
        'game_id' => 1,
        'package_type' => 0,
        'package_url' => '/uploads/game_packages/test_game_v1.0.0.zip',
        'version' => '1.0.0',
        'expected_url_pattern' => '/game/[^/]+/1\.0\.0/index\.html'
    ],
    [
        'name' => '第三方包测试',
        'game_id' => 2,
        'package_type' => 1,
        'package_url' => 'https://example.com/games/test-game/index.html',
        'version' => '2.0.0',
        'expected_url_pattern' => 'https://example\.com/games/test-game/index\.html'
    ]
];

/**
 * 测试游戏包预览URL构建
 */
function testGamePackagePreview($testCase) {
    echo "测试: {$testCase['name']}\n";
    echo "包类型: " . ($testCase['package_type'] === 0 ? '本地包' : '第三方包') . "\n";
    echo "包地址: {$testCase['package_url']}\n";
    echo "版本: {$testCase['version']}\n";
    
    // 模拟游戏包对象
    $gamePackage = new stdClass();
    $gamePackage->type = $testCase['package_type'];
    $gamePackage->package_url = $testCase['package_url'];
    $gamePackage->version = $testCase['version'];
    $gamePackage->game_id = $testCase['game_id'];
    
    // 模拟游戏对象
    $game = new stdClass();
    $game->code = 'test_game_' . $testCase['game_id'];
    $game->id = $testCase['game_id'];
    
    // 测试URL构建逻辑
    if (1 === (int) $gamePackage->type) {
        // 第三方包：直接使用package_url
        $url = $gamePackage->package_url;
    } else {
        // 本地包：使用传统路径
        $url = "/game/{$game->code}/{$gamePackage->version}/index.html";
    }
    
    echo "生成的URL: $url\n";
    
    // 验证URL格式
    $pattern = $testCase['expected_url_pattern'];
    if (preg_match("#$pattern#", $url)) {
        echo "✅ URL格式正确\n";
        return true;
    } else {
        echo "❌ URL格式错误，期望匹配: $pattern\n";
        return false;
    }
}

/**
 * 测试前端游戏URL构建
 */
function testGameUrlBuilding($testCase) {
    echo "\n测试前端游戏URL构建: {$testCase['name']}\n";
    
    // 模拟游戏包对象
    $gamePackage = new stdClass();
    $gamePackage->type = $testCase['package_type'];
    $gamePackage->package_url = $testCase['package_url'];
    $gamePackage->version = $testCase['version'];
    
    // 模拟游戏对象
    $game = new stdClass();
    $game->code = 'test_game_' . $testCase['game_id'];
    $game->latest_version = $testCase['version'];
    
    $channelCode = 'test_channel';
    
    // 测试URL构建逻辑（模拟buildGameUrl方法）
    // 注意：latest_version和游戏包都不会为空，所以直接使用
    if (1 === (int) $gamePackage->type) {
        // 第三方包：直接使用package_url，添加渠道参数
        $url = $gamePackage->package_url;
        $separator = strpos($url, '?') !== false ? '&' : '?';
        $url = $url . $separator . "channel={$channelCode}";
    } else {
        // 本地包：使用传统路径
        $url = "/game/{$game->code}/{$game->latest_version}/index.html?channel={$channelCode}";
    }
    
    echo "生成的游戏URL: $url\n";
    
    // 验证渠道参数是否正确添加
    if (strpos($url, "channel={$channelCode}") !== false) {
        echo "✅ 渠道参数添加正确\n";
        return true;
    } else {
        echo "❌ 渠道参数添加失败\n";
        return false;
    }
}

// 执行测试
$passedTests = 0;
$totalTests = count($testCases) * 2; // 每个测试用例有2个测试

foreach ($testCases as $testCase) {
    echo str_repeat('-', 50) . "\n";
    
    if (testGamePackagePreview($testCase)) {
        $passedTests++;
    }
    
    if (testGameUrlBuilding($testCase)) {
        $passedTests++;
    }
    
    echo "\n";
}

// 测试结果汇总
echo str_repeat('=', 50) . "\n";
echo "测试结果汇总:\n";
echo "通过测试: $passedTests / $totalTests\n";

if ($passedTests === $totalTests) {
    echo "🎉 所有测试通过！游戏包预览功能工作正常。\n";
} else {
    echo "⚠️  有测试失败，请检查实现。\n";
}

echo "\n测试完成。\n";
