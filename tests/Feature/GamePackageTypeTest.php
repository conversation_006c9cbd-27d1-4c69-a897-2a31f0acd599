<?php

declare(strict_types=1);

namespace Tests\Feature;

use PHPUnit\Framework\TestCase;

/**
 * 游戏包类型功能测试
 */
class GamePackageTypeTest extends TestCase
{
    /**
     * 测试第三方包URL验证 - 有效URL
     */
    public function testValidThirdPartyUrl(): void
    {
        $validUrls = [
            'https://example.com/game.zip',
            'http://example.com/game.zip',
            'https://cdn.example.com/games/v1.0/game.zip',
            'http://*************/game.zip',
        ];

        foreach ($validUrls as $url) {
            $this->assertTrue($this->validateThirdPartyUrl($url), "URL应该有效: {$url}");
        }
    }

    /**
     * 测试第三方包URL验证 - 无效URL
     */
    public function testInvalidThirdPartyUrl(): void
    {
        $invalidUrls = [
            '',
            'ftp://example.com/game.zip',
            'example.com/game.zip',
            'www.example.com/game.zip',
            'file:///local/game.zip',
            'javascript:alert(1)',
        ];

        foreach ($invalidUrls as $url) {
            $this->assertFalse($this->validateThirdPartyUrl($url), "URL应该无效: {$url}");
        }
    }

    /**
     * 测试包类型验证逻辑
     */
    public function testPackageTypeValidation(): void
    {
        // 测试本地包类型（不需要URL验证）
        $this->assertTrue($this->validatePackageType('0', ''));
        $this->assertTrue($this->validatePackageType('0', '/local/file.zip'));

        // 测试第三方包类型（需要URL验证）
        $this->assertTrue($this->validatePackageType('1', 'https://example.com/game.zip'));
        $this->assertFalse($this->validatePackageType('1', ''));
        $this->assertFalse($this->validatePackageType('1', 'invalid-url'));
    }

    /**
     * 模拟第三方包URL验证逻辑
     */
    private function validateThirdPartyUrl(string $url): bool
    {
        if (empty($url)) {
            return false;
        }

        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        if (!preg_match('/^https?:\/\/.+/', $url)) {
            return false;
        }

        return true;
    }

    /**
     * 模拟包类型验证逻辑
     */
    private function validatePackageType(string $type, string $packageUrl): bool
    {
        if ($type === '1') {
            // 第三方包需要验证URL
            return $this->validateThirdPartyUrl($packageUrl);
        }

        // 本地包不需要URL验证
        return true;
    }

    /**
     * 测试前端验证正则表达式
     */
    public function testFrontendValidationRegex(): void
    {
        $pattern = '/^https?:\/\/.+/';

        $validUrls = [
            'https://example.com',
            'http://example.com',
            'https://example.com/path',
            'http://192.168.1.1',
        ];

        $invalidUrls = [
            '',
            'ftp://example.com',
            'example.com',
            'www.example.com',
        ];

        foreach ($validUrls as $url) {
            $this->assertEquals(1, preg_match($pattern, $url), "前端正则应该匹配: {$url}");
        }

        foreach ($invalidUrls as $url) {
            $this->assertEquals(0, preg_match($pattern, $url), "前端正则不应该匹配: {$url}");
        }
    }
}
