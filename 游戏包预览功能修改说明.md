# 游戏包预览功能修改说明

## 概述

本次修改为游戏包管理系统添加了对第三方包的预览支持，使系统能够根据游戏包类型（本地包/第三方包）动态构建正确的预览URL。

## 修改内容

### 1. 游戏包预览功能 (GamePackageController)

**文件**: `app/admin/controller/GamePackageController.php`

**修改内容**:
- 添加了 `buildPreviewUrl()` 方法，根据游戏包类型构建不同的预览URL
- 本地包：使用传统的 `/game/{game_code}/{version}/index.html` 路径
- 第三方包：直接使用 `package_url` 字段的完整URL

**核心逻辑**:
```php
private function buildPreviewUrl(GamePackage $gamePackage, Game $game): string
{
    // 第三方包类型：直接使用package_url作为预览地址
    if (1 === (int) $gamePackage->type) {
        return $gamePackage->package_url;
    }

    // 本地包类型：使用传统的本地路径构建方式
    return '/game/' . $game->code . '/' . $gamePackage->version . '/index.html';
}
```

### 2. GamePackageService 服务增强

**文件**: `app/service/GamePackageService.php`

**新增方法**:
- `getGamePackageByGameIdAndVersion()`: 根据游戏ID和版本获取游戏包
- `getLatestReleasedGamePackage()`: 获取游戏的最新发布版本游戏包
- `getLatestGamePackage()`: 获取游戏的最新游戏包（无论是否发布）

### 3. 前端游戏展示功能 (GameController)

**文件**: `app/controller/GameController.php`

**修改内容**:
- 添加了 `GamePackageService` 依赖注入
- 添加了 `buildGameUrl()` 方法，根据游戏包类型构建游戏URL
- 支持第三方包的完整URL，并正确添加渠道参数

**核心逻辑**:
```php
private function buildGameUrl($game, string $channelCode): string
{
    // 获取最新版本的游戏包信息（latest_version不会为空）
    $gamePackage = $this->gamePackageService->getGamePackageByGameIdAndVersion(
        $game->id,
        $game->latest_version
    );

    // 如果找不到游戏包，使用默认路径
    if (!$gamePackage) {
        return "/game/{$game->code}/{$game->latest_version}/index.html?channel={$channelCode}";
    }

    // 根据包类型构建URL
    if (1 === (int) $gamePackage->type) {
        // 第三方包：直接使用package_url，添加渠道参数
        $url = $gamePackage->package_url;
        $separator = strpos($url, '?') !== false ? '&' : '?';
        return $url . $separator . "channel={$channelCode}";
    }

    // 本地包：使用传统路径
    return "/game/{$game->code}/{$game->latest_version}/index.html?channel={$channelCode}";
}
```

### 4. 游戏预览功能 (admin/GameController)

**修改说明**:
- **游戏预览地址保持不变**：仍然是 `/channel/{channel_code}/game/{game_code}`
- **不需要显示游戏包信息**：预览功能只提供渠道链接
- **实际的包类型处理**：在游戏show页面的iframe中根据包类型加载不同地址

**核心理念**:
游戏预览功能只是提供访问链接，真正的包类型区分处理在前端游戏展示页面中进行。

## 测试验证

创建了测试脚本 `test_game_package_preview.php` 来验证功能：

**测试用例**:
1. 本地包测试：验证本地包URL构建正确性
2. 第三方包测试：验证第三方包URL构建正确性
3. 渠道参数测试：验证渠道参数正确添加

**测试结果**: ✅ 所有测试通过

## 功能特性

### 支持的包类型
- **本地包 (type=0)**: 使用传统的本地文件路径
- **第三方包 (type=1)**: 使用完整的外部URL

### URL构建规则
- **本地包预览**: `/game/{game_code}/{version}/index.html`
- **第三方包预览**: 直接使用 `package_url`
- **游戏展示URL**: 自动添加渠道参数 `?channel={channel_code}`

### 兼容性
- 完全向后兼容现有的本地包功能
- 新增的第三方包功能不影响现有数据
- 保持现有的API接口不变

## 使用说明

1. **管理员预览游戏包**:
   - 在游戏包管理页面点击"预览"按钮
   - 系统会根据包类型自动构建正确的预览URL

2. **管理员预览游戏**:
   - 在游戏管理页面点击"预览"按钮
   - 页面会显示当前游戏包信息和所有渠道的预览链接

3. **用户访问游戏**:
   - 通过渠道链接访问游戏时，系统会自动根据包类型加载正确的游戏资源

## 注意事项

- 第三方包的URL必须是完整的、可访问的HTTP/HTTPS地址
- 渠道参数会自动添加到游戏URL中，第三方包需要支持接收此参数
- 建议第三方包支持跨域访问，以确保在iframe中正常加载
