<?php

declare(strict_types=1);

namespace app\controller;

use app\dto\GameAuthRequest;
use app\service\ChannelService;
use app\service\GameService;
use app\service\GamePackageService;
use app\service\GameUserAuthService;
use app\utils\ArrayToolkit;
use DI\Attribute\Inject;

/**
 * 游戏展示页面控制器.
 */
class GameController
{
    use BaseTraitController;

    protected array $noNeedLogin = ['show'];

    #[Inject()]
    protected ChannelService $channelService;

    #[Inject()]
    protected GameService $gameService;

    #[Inject()]
    protected GamePackageService $gamePackageService;

    #[Inject()]
    protected GameUserAuthService $gameUserAuthService;

    /**
     * 游戏展示页面.
     *
     * 处理游戏展示请求，包括游戏用户登录.
     *
     * @param mixed $channel 渠道代码
     * @param mixed $game 游戏代码
     *
     * @return mixed 响应结果
     */
    public function show($channel, $game)
    {
        // 1. 验证渠道和游戏
        $channel = $this->channelService->getChannelByCode((string) $channel);
        $game = $this->gameService->getGameByCode((int) $game);
        // 是否需要接入 sdk
        $needSdk = request()->get('need_sdk', 1);

        if (!$channel || !$game || !ArrayToolkit::isIdInCommaList($game->channel_ids, $channel->id)) {
            return redirect('/404');
        }

        // 2. 处理游戏用户登录（如果用户未登录且有用户标识参数）
        $this->handleGameUserLogin($channel->id, $game->id);

        // 3. 构建游戏URL
        $url = $this->buildGameUrl($game, $channel->code);

        return view('game/show', [
            'channel' => $channel,
            'game' => $game,
            'needSdk' => $needSdk,
            'url' => $url,
        ]);
    }

    /**
     * 处理游戏用户登录.
     *
     * 如果用户未登录且请求中包含用户标识参数，则登录用户.
     *
     * @param int $channelId 渠道ID
     * @param int $gameId 游戏ID
     */
    private function handleGameUserLogin(int $channelId, int $gameId): void
    {
        try {
            // 解析用户标识
            $userIdentifier = $this->gameUserAuthService->resolveUserIdentifier($channelId);

            // 创建登录请求
            $loginRequest = $this->createLoginRequest($channelId, $gameId, $userIdentifier);

            // 执行登录并触发登录事件
            $this->gameUserAuthService->handleLogin($loginRequest);
        } catch (\Throwable $e) {
            // 登录失败时记录日志但不影响游戏展示
            \error_log('Login failed: ' . $e->getMessage(), 4);
        }
    }

    /**
     * 根据游戏包类型构建游戏URL
     *
     * @param \app\admin\model\Game $game 游戏对象
     * @param string $channelCode 渠道代码
     * @return string 游戏URL
     */
    private function buildGameUrl($game, string $channelCode): string
    {
        // 获取最新版本的游戏包信息
        $gamePackage = $this->gamePackageService->getGamePackageByGameIdAndVersion(
            $game->id,
            $game->latest_version
        );

        // 根据包类型构建URL
        if (1 === (int) $gamePackage->type) {
            // 第三方包：直接使用package_url，添加渠道参数
            $url = $gamePackage->package_url;
            $separator = strpos($url, '?') !== false ? '&' : '?';
            return $url . $separator . "channel={$channelCode}";
        }

        // 本地包：使用传统路径
        return "/game/{$game->code}/{$game->latest_version}/index.html?channel={$channelCode}";
    }

    /**
     * 创建游戏认证请求对象.
     *
     * @param int $channelId 渠道ID
     * @param string $userIdentifier 用户标识
     * @param int $gameId 游戏ID
     *
     * @return GameAuthRequest 游戏认证请求对象
     */
    private function createLoginRequest(int $channelId, int $gameId, string $userIdentifier): GameAuthRequest
    {
        return GameAuthRequest::create(
            channelId: $channelId,
            gameId: $gameId,
            username: $userIdentifier,
        );
    }
}
