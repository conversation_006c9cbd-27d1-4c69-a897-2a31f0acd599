<?php

declare(strict_types=1);

namespace app\service;

use app\admin\model\GamePackage;

class GamePackageService
{
    /**
     * Summary of getGamePackage.
     *
     * @param mixed $id
     */
    public function getGamePackage($id): ?GamePackage
    {
        $result = GamePackage::find($id);

        return $result instanceof GamePackage ? $result : null;
    }

    /**
     * 根据游戏ID和版本获取游戏包
     *
     * @param int $gameId 游戏ID
     * @param string $version 版本号
     * @return GamePackage|null
     */
    public function getGamePackageByGameIdAndVersion(int $gameId, string $version): ?GamePackage
    {
        $result = GamePackage::where('game_id', $gameId)
            ->where('version', $version)
            ->first();

        return $result instanceof GamePackage ? $result : null;
    }

    /**
     * 获取游戏的最新发布版本游戏包
     *
     * @param int $gameId 游戏ID
     * @return GamePackage|null
     */
    public function getLatestReleasedGamePackage(int $gameId): ?GamePackage
    {
        $result = GamePackage::where('game_id', $gameId)
            ->where('is_release', 1)
            ->orderBy('created_at', 'desc')
            ->first();

        return $result instanceof GamePackage ? $result : null;
    }

    /**
     * 获取游戏的最新游戏包（无论是否发布）
     *
     * @param int $gameId 游戏ID
     * @return GamePackage|null
     */
    public function getLatestGamePackage(int $gameId): ?GamePackage
    {
        $result = GamePackage::where('game_id', $gameId)
            ->orderBy('created_at', 'desc')
            ->first();

        return $result instanceof GamePackage ? $result : null;
    }
}
