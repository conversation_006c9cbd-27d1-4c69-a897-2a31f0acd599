<!DOCTYPE html>
<html lang="zh-cn">

<head>
  <meta charset="UTF-8">
  <title>预览页面</title>
  <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
  <link rel="stylesheet" href="/app/admin/component/jsoneditor/css/jsoneditor.css" />
  <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
</head>

<body>
  <!-- 遍历channels -->
  <div class="main-container">
    <?php foreach ($channels as $channel): ?>
    <div class="layui-form-item">
      <span class="layui-form-label">渠道: </span>
      <div class="layui-input-block" style="line-height: 38px;">
        <?= htmlspecialchars($channel['name'] ?? '') ?>
      </div>
    </div>
    <div class="layui-form-item">
      <span class="layui-form-label">预览 URL: </span>
      <a style="line-height: 38px;" href="/channel/<?= htmlspecialchars($channel->code ?? '') ?>/game/<?= htmlspecialchars($game->code ?? '') ?>" target="_blank">/channel/<?= htmlspecialchars($channel->code ?? '') ?>/game/<?= htmlspecialchars($game->code ?? '') ?></a>
    </div>
    <?php endforeach; ?>
  </div>

  <script src="/app/admin/component/layui/layui.js?v=2.8.12"></script>
  <script src="/app/admin/component/pear/pear.js"></script>
  <script src="/app/admin/component/jsoneditor/jsoneditor.js"></script>
  <script src="/app/admin/admin/js/permission.js"></script>
</body>

</html>