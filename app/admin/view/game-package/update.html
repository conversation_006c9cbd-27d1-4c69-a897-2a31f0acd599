<!DOCTYPE html>
<html lang="zh-cn">
    <head>
        <meta charset="UTF-8">
        <title>更新页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/component/jsoneditor/css/jsoneditor.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
        
    </head>
    <body>

        <form class="layui-form">

            <div class="mainBox">
                <div class="main-container mr-5">
                    <div class="layui-form-item">
                        <label class="layui-form-label">类型</label>
                        <div class="layui-input-block">
                            <div name="type" id="type" value="" ></div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">版本</label>
                        <div class="layui-input-block">
                            <input type="number" name="version" value="" required lay-verify="required" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">版本描述</label>
                        <div class="layui-input-block">
                            <input type="text" name="description" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">包地址</label>
                        <div class="layui-input-block">
                            <!-- 本地包文件上传区域 -->
                            <div id="local-package-area">
                                <span></span>
                                <input type="text" style="display:none" name="package_url" value="" />
                                <button type="button" class="pear-btn pear-btn-primary pear-btn-sm" id="package_url" permission="app.admin.upload.file">
                                    <i class="layui-icon layui-icon-upload"></i>上传文件
                                </button>
                                <button type="button" class="pear-btn pear-btn-primary pear-btn-sm" id="attachment-choose-package_url" permission="app.admin.upload.attachment">
                                    <i class="layui-icon layui-icon-align-left"></i>选择文件
                                </button>
                            </div>
                            <!-- 第三方包URL输入区域 -->
                            <div id="third-party-package-area" style="display:none">
                                <input type="text" name="package_url_input" value="" placeholder="请输入第三方包地址，必须以 https:// 或 http:// 开头" class="layui-input">
                                <div class="layui-form-mid layui-word-aux">请输入有效的第三方包下载地址</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">是否发布</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="is_release" lay-filter="is_release" lay-skin="switch" />
                            <input type="text" style="display:none" name="is_release" value="0" />
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">发布时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="release_time" id="release_time" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                </div>
            </div>

            <div class="bottom">
                <div class="button-container">
                    <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit="" lay-filter="save">
                        提交
                    </button>
                    <button type="reset" class="pear-btn pear-btn-md">
                        重置
                    </button>
                </div>
            </div>
            
            <input type="number" name="game_id" hidden value="<?php echo $game->id ?>" required lay-verify="required">
        </form>

        <script src="/app/admin/component/layui/layui.js?v=2.8.12"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/component/jsoneditor/jsoneditor.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        
        <script>

            // 相关接口
            const PRIMARY_KEY = "id";
            const SELECT_API = "/admin/game-package/select" + location.search;
            const UPDATE_API = "/admin/game-package/update";
            // 获取数据库记录
            layui.use(["form", "util", "popup"], function () {
                let $ = layui.$;
                $.ajax({
                    url: SELECT_API,
                    dataType: "json",
                    success: function (res) {
                        
                        // 给表单初始化数据
                        layui.each(res.data[0], function (key, value) {
                            let obj = $('*[name="'+key+'"]');
                            if (key === "password") {
                                obj.attr("placeholder", "不更新密码请留空");
                                return;
                            }
                            if (typeof obj[0] === "undefined" || !obj[0].nodeName) return;
                            if (obj[0].nodeName.toLowerCase() === "textarea") {
                                obj.val(value);
                            } else {
                                obj.attr("value", value);
                                obj[0].value = value;
                            }
                            
                            // 多图渲染
                            if (obj[0].classList.contains('uploader-list')) {
                                let multiple_images = value.split(",");
                                $.each(multiple_images, function(index, value) {
                                    $('#uploader-list-'+ key).append(
                                        '<div class="file-iteme">' +
                                        '<div class="handle"><i class="layui-icon layui-icon-delete"></i></div>' +
                                        '<img src='+value +' alt="'+ value +'" >' +
                                        '</div>'
                                    );
                                });
                            }
                        });
                        
                        // 字段 类型 type
                        layui.use(["jquery", "xmSelect", "popup"], function() {
                            layui.$.ajax({
                                url: "/app/admin/dict/get/game_package_type",
                                dataType: "json",
                                success: function (res) {
                                    let value = layui.$("#type").attr("value");
                                    let initValue = value ? value.split(",") : [];
                                    layui.xmSelect.render({
                                        el: "#type",
                                        name: "type",
                                        initValue: initValue,
                                        filterable: true,
                                        data: res.data,
                                        model: {"icon":"hidden","label":{"type":"text"}},
                                        clickClose: true,
                                        radio: true,
                                        on: function(data) {
                                            // 监听类型选择变化
                                            try {
                                                let selectedValue = data.arr && data.arr[0] ? data.arr[0].value : "";
                                                if (selectedValue !== "") {
                                                    togglePackageInputArea(selectedValue);
                                                }
                                            } catch (error) {
                                                console.error("xmSelect事件处理错误:", error);
                                            }
                                        }
                                    });

                                    // 根据当前值初始化显示区域
                                    var currentType = initValue.length > 0 ? initValue[0] : "0";
                                    togglePackageInputArea(currentType);

                                    // 如果是第三方包，需要设置URL输入框的值
                                    if (currentType === "1") {
                                        var currentPackageUrl = layui.$('input[name="package_url"]').val();
                                        layui.$('input[name="package_url_input"]').val(currentPackageUrl);
                                    }

                                    if (res.code) {
                                        layui.popup.failure(res.msg);
                                    }
                                }
                            });
                        });

                        // 切换包地址输入区域显示
                        function togglePackageInputArea(packageType) {
                            var localArea = layui.$("#local-package-area");
                            var thirdPartyArea = layui.$("#third-party-package-area");
                            var packageUrlInput = layui.$('input[name="package_url"]');
                            var packageUrlTextInput = layui.$('input[name="package_url_input"]');

                            if (packageType === "1") {
                                // 第三方包：显示URL输入框，隐藏文件上传
                                localArea.hide();
                                thirdPartyArea.show();
                                // 如果切换到第三方包，将当前包地址值复制到URL输入框
                                if (packageUrlInput.val() && !packageUrlTextInput.val()) {
                                    packageUrlTextInput.val(packageUrlInput.val());
                                }
                                // 为第三方包URL输入框添加验证规则
                                packageUrlTextInput.attr("lay-verify", "required|thirdPartyUrl");
                            } else {
                                // 本地包：显示文件上传，隐藏URL输入框
                                localArea.show();
                                thirdPartyArea.hide();
                                packageUrlTextInput.val(""); // 清空URL输入的值
                                // 移除第三方包URL输入框的验证规则
                                packageUrlTextInput.removeAttr("lay-verify");
                            }
                        }
                        
                        // 字段 包地址 package_url
                        layui.use(["upload", "layer", "popup", "util"], function() {
                            let input = layui.$("#package_url").prev();
                            input.prev().html(layui.util.escape(input.val()));
                            layui.$("#attachment-choose-package_url").on("click", function() {
                                parent.layer.open({
                                    type: 2,
                                    title: "选择附件",
                                    content: "/app/admin/upload/attachment",
                                    area: ["95%", "90%"],
                                    success: function (layero, index) {
                                        parent.layui.$("#layui-layer" + index).data("callback", function (data) {
                                            input.val(data.url).prev().html(layui.util.escape(data.url));
                                        });
                                    }
                                });
                            });
                            layui.upload.render({
                                elem: "#package_url",
                                accept: "file",
                                url: "/app/admin/upload/file",
                                field: "__file__",
                                done: function (res) {
                                    if (res.code) return layui.popup.failure(res.msg);
                                    this.item.prev().val(res.data.url).prev().html(layui.util.escape(res.data.url));
                                }
                            });
                        });
                        
                        // 字段 是否发布 is_release
                        layui.use(["form"], function() {
                            layui.$("#is_release").attr("checked", layui.$('input[name="is_release"]').val() != 0);
                            layui.form.render();
                            layui.form.on("switch(is_release)", function(data) {
                                layui.$('input[name="is_release"]').val(this.checked ? 1 : 0);
                            });
                        })
                        
                        // 字段 发布时间 release_time
                        layui.use(["laydate"], function() {
                            layui.laydate.render({
                                elem: "#release_time",
                                type: "datetime",
                            });
                        })
                        
                        
                        // ajax返回失败
                        if (res.code) {
                            layui.popup.failure(res.msg);
                        }
                        
                    }
                });
            });

            //提交事件
            layui.use(["form", "popup"], function () {
                // 字段验证允许为空
                layui.form.verify({
                    phone: [/(^$)|^1\d{10}$/, "请输入正确的手机号"],
                    email: [/(^$)|^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/, "邮箱格式不正确"],
                    url: [/(^$)|(^#)|(^http(s*):\/\/[^\s]+\.[^\s]+)/, "链接格式不正确"],
                    number: [/(^$)|^\d+$/,'只能填写数字'],
                    date: [/(^$)|^(\d{4})[-\/](\d{1}|0\d{1}|1[0-2])([-\/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/, "日期格式不正确"],
                    identity: [/(^$)|(^\d{15}$)|(^\d{17}(x|X|\d)$)/, "请输入正确的身份证号"],
                    thirdPartyUrl: [/^https?:\/\/.+/, "第三方包地址必须以 https:// 或 http:// 开头"]
                });
                layui.form.on("submit(save)", function (data) {
                    // 处理包地址字段：根据包类型合并不同的输入值
                    var formData = data.field;
                    var packageType = formData.type;

                    if (packageType === "1") {
                        // 第三方包：使用URL输入框的值
                        formData.package_url = formData.package_url_input || "";
                    }
                    // 本地包：使用原有的 package_url 字段值

                    // 删除临时字段
                    delete formData.package_url_input;

                    formData[PRIMARY_KEY] = layui.url().search[PRIMARY_KEY];
                    layui.$.ajax({
                        url: UPDATE_API,
                        type: "POST",
                        dateType: "json",
                        data: formData,
                        success: function (res) {
                            if (res.code) {
                                return layui.popup.failure(res.msg);
                            }
                            return layui.popup.success("操作成功", function () {
                                parent.refreshTable();
                                parent.layer.close(parent.layer.getFrameIndex(window.name));
                            });
                        }
                    });
                    return false;
                });
            });

        </script>

    </body>

</html>
