<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\model\Game;
use app\admin\model\GamePackage;
use app\service\GamePackageService;
use app\service\GameService;
use DI\Attribute\Inject;
use plugin\admin\app\controller\Crud;
use support\exception\BusinessException;
use support\Request;
use support\Response;

/**
 * 游戏包管理.
 */
class GamePackageController extends Crud
{
    /**
     * @var GamePackage
     */
    protected $model = null;

    #[Inject()]
    protected GameService $gameService;

    #[Inject()]
    protected GamePackageService $gamePackageService;

    /**
     * 构造函数.
     *
     * @return void
     */
    public function __construct()
    {
        $this->model = new GamePackage();
    }

    /**
     * 浏览.
     */
    public function index(Request $request): Response
    {
        $gameId = $request->get('game_id');

        return view('game-package/index', ['game_id' => $gameId]);
    }

    /**
     * 插入.
     *
     * @throws BusinessException
     */
    public function insert(Request $request): Response
    {
        if ('POST' === $request->method()) {
            // 验证包地址格式
            $this->validatePackageUrl($request);
            return parent::insert($request);
        }

        $gameId = (int) $request->get('game_id');
        $game = $this->gameService->getGame($gameId);

        return view('game-package/insert', ['game' => $game]);
    }

    /**
     * 更新.
     *
     * @throws BusinessException
     */
    public function update(Request $request): Response
    {
        if ('POST' === $request->method()) {
            // 验证包地址格式
            $this->validatePackageUrl($request);
            return parent::update($request);
        }

        $gameId = (int) $request->get('game_id');
        $game = $this->gameService->getGame($gameId);

        return view('game-package/update', ['game' => $game]);
    }

    public function preview(Request $request): Response
    {
        $id = $request->get('id');
        $gamePackage = $this->gamePackageService->getGamePackage($id);
        if (!$gamePackage instanceof GamePackage) {
            return redirect('/404');
        }

        $game = $this->gameService->getGame($gamePackage->game_id);
        if (!$game instanceof Game) {
            return redirect('/404');
        }

        // 根据包类型构建不同的预览URL
        $url = $this->buildPreviewUrl($gamePackage, $game);

        return view('game-package/preview', ['url' => $url]);
    }

    /**
     * 根据包类型构建预览URL
     *
     * @param GamePackage $gamePackage
     * @param Game $game
     * @return string
     */
    private function buildPreviewUrl(GamePackage $gamePackage, Game $game): string
    {
        // 第三方包类型：直接使用package_url作为预览地址
        if (1 === (int) $gamePackage->type) {
            return $gamePackage->package_url;
        }

        // 本地包类型：使用传统的本地路径构建方式
        return '/game/' . $game->code . '/' . $gamePackage->version . '/index.html';
    }

    /**
     * 验证包地址格式
     *
     * @param Request $request
     * @throws BusinessException
     */
    private function validatePackageUrl(Request $request): void
    {
        $type = $request->post('type');
        $packageUrl = $request->post('package_url');

        // 第三方包类型需要验证URL格式
        if ('1' === $type) {
            if (empty($packageUrl)) {
                throw new BusinessException('第三方包地址不能为空', 400);
            }

            if (!filter_var($packageUrl, FILTER_VALIDATE_URL)) {
                throw new BusinessException('第三方包地址格式不正确', 400);
            }

            if (!preg_match('/^https?:\/\/.+/', $packageUrl)) {
                throw new BusinessException('第三方包地址必须以 https:// 或 http:// 开头', 400);
            }
        }
        // 本地包类型的验证保持原有逻辑（文件上传验证）
    }
}
